package com.sheep.mq;

import com.rabbitmq.client.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeoutException;

/**
 * RabbitMQ工具类
 * 提供RabbitMQ的基本操作功能，包括连接管理、队列操作、消息发送和接收
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class RabbitMQUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(RabbitMQUtil.class);
    
    // 默认连接参数
    private static final String DEFAULT_HOST = "localhost";
    private static final int DEFAULT_PORT = 5672;
    private static final String DEFAULT_USERNAME = "guest";
    private static final String DEFAULT_PASSWORD = "guest";
    private static final String DEFAULT_VIRTUAL_HOST = "/";
    
    private Connection connection;
    private Channel channel;
    
    /**
     * 使用默认参数构造RabbitMQUtil
     */
    public RabbitMQUtil() {
        this(DEFAULT_HOST, DEFAULT_PORT, DEFAULT_USERNAME, DEFAULT_PASSWORD, DEFAULT_VIRTUAL_HOST);
    }
    
    /**
     * 使用指定参数构造RabbitMQUtil
     * 
     * @param host RabbitMQ服务器地址
     * @param port RabbitMQ服务器端口
     * @param username 用户名
     * @param password 密码
     * @param virtualHost 虚拟主机
     */
    public RabbitMQUtil(String host, int port, String username, String password, String virtualHost) {
        try {
            ConnectionFactory factory = new ConnectionFactory();
            factory.setHost(host);
            factory.setPort(port);
            factory.setUsername(username);
            factory.setPassword(password);
            factory.setVirtualHost(virtualHost);
            
            this.connection = factory.newConnection();
            this.channel = connection.createChannel();
            
            logger.info("RabbitMQ连接成功建立: {}:{}", host, port);
        } catch (IOException | TimeoutException e) {
            logger.error("RabbitMQ连接失败", e);
            throw new RuntimeException("RabbitMQ连接失败", e);
        }
    }
    
    /**
     * 声明队列
     * 
     * @param queueName 队列名称
     * @param durable 是否持久化
     * @param exclusive 是否排他
     * @param autoDelete 是否自动删除
     * @return 队列声明结果
     */
    public AMQP.Queue.DeclareOk declareQueue(String queueName, boolean durable, boolean exclusive, boolean autoDelete) {
        try {
            AMQP.Queue.DeclareOk result = channel.queueDeclare(queueName, durable, exclusive, autoDelete, null);
            logger.info("队列声明成功: {}", queueName);
            return result;
        } catch (IOException e) {
            logger.error("队列声明失败: {}", queueName, e);
            throw new RuntimeException("队列声明失败", e);
        }
    }
    
    /**
     * 声明持久化队列（默认参数）
     * 
     * @param queueName 队列名称
     * @return 队列声明结果
     */
    public AMQP.Queue.DeclareOk declareQueue(String queueName) {
        return declareQueue(queueName, true, false, false);
    }
    
    /**
     * 发送消息到队列
     * 
     * @param queueName 队列名称
     * @param message 消息内容
     */
    public void sendMessage(String queueName, String message) {
        try {
            channel.basicPublish("", queueName, null, message.getBytes(StandardCharsets.UTF_8));
            logger.info("消息发送成功到队列 {}: {}", queueName, message);
        } catch (IOException e) {
            logger.error("消息发送失败到队列 {}: {}", queueName, message, e);
            throw new RuntimeException("消息发送失败", e);
        }
    }
    
    /**
     * 发送持久化消息到队列
     * 
     * @param queueName 队列名称
     * @param message 消息内容
     */
    public void sendPersistentMessage(String queueName, String message) {
        try {
            AMQP.BasicProperties properties = MessageProperties.PERSISTENT_TEXT_PLAIN;
            channel.basicPublish("", queueName, properties, message.getBytes(StandardCharsets.UTF_8));
            logger.info("持久化消息发送成功到队列 {}: {}", queueName, message);
        } catch (IOException e) {
            logger.error("持久化消息发送失败到队列 {}: {}", queueName, message, e);
            throw new RuntimeException("持久化消息发送失败", e);
        }
    }
    
    /**
     * 接收单条消息（手动确认）
     * 
     * @param queueName 队列名称
     * @return 消息内容，如果没有消息返回null
     */
    public String receiveMessage(String queueName) {
        try {
            GetResponse response = channel.basicGet(queueName, false);
            if (response != null) {
                String message = new String(response.getBody(), StandardCharsets.UTF_8);
                // 手动确认消息
                channel.basicAck(response.getEnvelope().getDeliveryTag(), false);
                logger.info("从队列 {} 接收消息: {}", queueName, message);
                return message;
            }
            return null;
        } catch (IOException e) {
            logger.error("从队列 {} 接收消息失败", queueName, e);
            throw new RuntimeException("接收消息失败", e);
        }
    }
    
    /**
     * 设置消息消费者（异步接收）
     * 
     * @param queueName 队列名称
     * @param callback 消息处理回调
     */
    public void setConsumer(String queueName, MessageCallback callback) {
        try {
            DeliverCallback deliverCallback = (consumerTag, delivery) -> {
                String message = new String(delivery.getBody(), StandardCharsets.UTF_8);
                try {
                    callback.handle(message);
                    // 手动确认消息
                    channel.basicAck(delivery.getEnvelope().getDeliveryTag(), false);
                    logger.info("消息处理成功: {}", message);
                } catch (Exception e) {
                    logger.error("消息处理失败: {}", message, e);
                    // 拒绝消息并重新入队
                    channel.basicNack(delivery.getEnvelope().getDeliveryTag(), false, true);
                }
            };
            
            channel.basicConsume(queueName, false, deliverCallback, consumerTag -> {});
            logger.info("消费者设置成功，队列: {}", queueName);
        } catch (IOException e) {
            logger.error("设置消费者失败，队列: {}", queueName, e);
            throw new RuntimeException("设置消费者失败", e);
        }
    }
    
    /**
     * 获取队列中的消息数量
     * 
     * @param queueName 队列名称
     * @return 消息数量
     */
    public long getMessageCount(String queueName) {
        try {
            AMQP.Queue.DeclareOk result = channel.queueDeclarePassive(queueName);
            return result.getMessageCount();
        } catch (IOException e) {
            logger.error("获取队列消息数量失败: {}", queueName, e);
            throw new RuntimeException("获取队列消息数量失败", e);
        }
    }
    
    /**
     * 清空队列
     * 
     * @param queueName 队列名称
     */
    public void purgeQueue(String queueName) {
        try {
            channel.queuePurge(queueName);
            logger.info("队列清空成功: {}", queueName);
        } catch (IOException e) {
            logger.error("队列清空失败: {}", queueName, e);
            throw new RuntimeException("队列清空失败", e);
        }
    }
    
    /**
     * 删除队列
     * 
     * @param queueName 队列名称
     */
    public void deleteQueue(String queueName) {
        try {
            channel.queueDelete(queueName);
            logger.info("队列删除成功: {}", queueName);
        } catch (IOException e) {
            logger.error("队列删除失败: {}", queueName, e);
            throw new RuntimeException("队列删除失败", e);
        }
    }
    
    /**
     * 关闭连接
     */
    public void close() {
        try {
            if (channel != null && channel.isOpen()) {
                channel.close();
            }
            if (connection != null && connection.isOpen()) {
                connection.close();
            }
            logger.info("RabbitMQ连接已关闭");
        } catch (IOException | TimeoutException e) {
            logger.error("关闭RabbitMQ连接失败", e);
        }
    }
    
    /**
     * 消息处理回调接口
     */
    @FunctionalInterface
    public interface MessageCallback {
        /**
         * 处理消息
         * 
         * @param message 消息内容
         * @throws Exception 处理异常
         */
        void handle(String message) throws Exception;
    }
}
