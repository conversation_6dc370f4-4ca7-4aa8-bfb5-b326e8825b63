package com.sheep.mq;

/**
 * RabbitMQ工具类使用示例
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class RabbitMQExample {
    
    public static void main(String[] args) {
        // 创建RabbitMQ工具实例（使用默认连接参数）
        RabbitMQUtil rabbitMQUtil = new RabbitMQUtil();
        
        String queueName = "test_queue";
        
        try {
            // 1. 声明队列
            rabbitMQUtil.declareQueue(queueName);
            System.out.println("队列声明成功: " + queueName);
            
            // 2. 发送消息
            rabbitMQUtil.sendMessage(queueName, "Hello, RabbitMQ!");
            rabbitMQUtil.sendMessage(queueName, "这是第二条消息");
            rabbitMQUtil.sendPersistentMessage(queueName, "这是一条持久化消息");
            System.out.println("消息发送完成");
            
            // 3. 获取队列消息数量
            long messageCount = rabbitMQUtil.getMessageCount(queueName);
            System.out.println("队列中的消息数量: " + messageCount);
            
            // 4. 接收消息
            System.out.println("开始接收消息:");
            String message;
            while ((message = rabbitMQUtil.receiveMessage(queueName)) != null) {
                System.out.println("接收到消息: " + message);
            }
            
            // 5. 设置异步消费者示例
            System.out.println("设置异步消费者...");
            rabbitMQUtil.sendMessage(queueName, "异步消息1");
            rabbitMQUtil.sendMessage(queueName, "异步消息2");
            
            rabbitMQUtil.setConsumer(queueName, message1 -> {
                System.out.println("异步处理消息: " + message1);
                // 这里可以添加具体的业务逻辑
            });
            
            // 等待一段时间让消费者处理消息
            Thread.sleep(2000);
            
            // 6. 清空队列
            rabbitMQUtil.purgeQueue(queueName);
            System.out.println("队列已清空");
            
            // 7. 删除队列
            rabbitMQUtil.deleteQueue(queueName);
            System.out.println("队列已删除");
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 8. 关闭连接
            rabbitMQUtil.close();
            System.out.println("连接已关闭");
        }
    }
    
    /**
     * 生产者示例
     */
    public static void producerExample() {
        RabbitMQUtil producer = new RabbitMQUtil();
        String queueName = "producer_queue";
        
        try {
            producer.declareQueue(queueName);
            
            // 发送多条消息
            for (int i = 1; i <= 10; i++) {
                producer.sendMessage(queueName, "消息 " + i);
            }
            
            System.out.println("生产者发送完成");
        } finally {
            producer.close();
        }
    }
    
    /**
     * 消费者示例
     */
    public static void consumerExample() {
        RabbitMQUtil consumer = new RabbitMQUtil();
        String queueName = "producer_queue";
        
        try {
            consumer.declareQueue(queueName);
            
            // 设置消费者
            consumer.setConsumer(queueName, message -> {
                System.out.println("消费者处理消息: " + message);
                // 模拟处理时间
                Thread.sleep(1000);
            });
            
            System.out.println("消费者已启动，按任意键退出...");
            System.in.read();
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            consumer.close();
        }
    }
}
