# RabbitMQ工具类使用说明

## 概述

`RabbitMQUtil` 是一个封装了RabbitMQ基本操作的工具类，提供了简单易用的API来进行消息队列的操作。

## 功能特性

- ✅ 连接管理（支持自定义连接参数）
- ✅ 队列声明和管理
- ✅ 消息发送（普通消息和持久化消息）
- ✅ 消息接收（同步和异步）
- ✅ 队列状态查询
- ✅ 队列清空和删除
- ✅ 完善的日志记录
- ✅ 异常处理

## 依赖要求

确保在 `pom.xml` 中添加了以下依赖：

```xml
<dependencies>
    <!-- RabbitMQ Java Client -->
    <dependency>
        <groupId>com.rabbitmq</groupId>
        <artifactId>amqp-client</artifactId>
        <version>5.20.0</version>
    </dependency>
    
    <!-- SLF4J API for logging -->
    <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>2.0.9</version>
    </dependency>
    
    <!-- SLF4J Simple implementation -->
    <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-simple</artifactId>
        <version>2.0.9</version>
    </dependency>
</dependencies>
```

## 环境准备

1. 安装并启动RabbitMQ服务器
2. 确保RabbitMQ服务运行在默认端口5672（或配置相应的连接参数）

## 基本使用

### 1. 创建工具类实例

```java
// 使用默认连接参数（localhost:5672, guest/guest）
RabbitMQUtil rabbitMQUtil = new RabbitMQUtil();

// 或者使用自定义连接参数
RabbitMQUtil rabbitMQUtil = new RabbitMQUtil("*************", 5672, "admin", "password", "/");
```

### 2. 队列操作

```java
// 声明队列（持久化）
rabbitMQUtil.declareQueue("my_queue");

// 声明队列（自定义参数）
rabbitMQUtil.declareQueue("my_queue", true, false, false);

// 获取队列消息数量
long count = rabbitMQUtil.getMessageCount("my_queue");

// 清空队列
rabbitMQUtil.purgeQueue("my_queue");

// 删除队列
rabbitMQUtil.deleteQueue("my_queue");
```

### 3. 发送消息

```java
// 发送普通消息
rabbitMQUtil.sendMessage("my_queue", "Hello, World!");

// 发送持久化消息
rabbitMQUtil.sendPersistentMessage("my_queue", "Important message");
```

### 4. 接收消息

```java
// 同步接收单条消息
String message = rabbitMQUtil.receiveMessage("my_queue");
if (message != null) {
    System.out.println("收到消息: " + message);
}

// 异步消费消息
rabbitMQUtil.setConsumer("my_queue", message -> {
    System.out.println("处理消息: " + message);
    // 添加业务逻辑
});
```

### 5. 资源清理

```java
// 关闭连接
rabbitMQUtil.close();
```

## 完整示例

参考 `RabbitMQExample.java` 文件中的示例代码。

## 注意事项

1. **连接管理**：使用完毕后记得调用 `close()` 方法关闭连接
2. **异常处理**：工具类会抛出 `RuntimeException`，请根据需要进行异常处理
3. **消息确认**：工具类默认使用手动确认模式，确保消息处理的可靠性
4. **线程安全**：单个 `RabbitMQUtil` 实例不是线程安全的，多线程环境下请为每个线程创建独立的实例
5. **资源释放**：建议在 try-with-resources 或 finally 块中调用 `close()` 方法

## 扩展功能

如需更高级的功能，可以考虑扩展以下特性：

- 交换机（Exchange）操作
- 路由键（Routing Key）支持
- 消息属性设置
- 死信队列配置
- 集群连接支持
- 连接池管理

## 故障排除

1. **连接失败**：检查RabbitMQ服务是否启动，网络连接是否正常
2. **权限错误**：确认用户名密码正确，用户有相应的权限
3. **队列不存在**：确保在操作前先声明队列
4. **消息丢失**：使用持久化消息和持久化队列来保证消息不丢失
